# SCINet自定义数据加载器

这个自定义DataLoader专门为SCINet模型设计，支持**三个输入参数、三个输出参数**的时间序列预测任务。

## 功能特点

- ✅ 支持3个输入特征 → 3个输出特征的映射
- ✅ 自动数据分割（训练/验证/测试集）
- ✅ 标准化处理（Z-score标准化）
- ✅ 与SCINet模型完全兼容
- ✅ 支持CSV和TXT格式数据
- ✅ 灵活的参数配置

## 快速开始

### 1. 数据格式要求

你的数据文件应该是CSV格式，包含6列：

```csv
input_1,input_2,input_3,output_1,output_2,output_3
1.23,4.56,7.89,2.34,5.67,8.90
2.34,5.67,8.90,3.45,6.78,9.01
...
```

### 2. 基本使用

```python
from data_process.custom_dataloader import CustomDataLoader
from models.SCINet import SCINet

# 创建数据加载器
data_loader = CustomDataLoader(
    data_path='your_data.csv',
    seq_len=96,                    # 输入序列长度
    pred_len=24,                   # 预测序列长度
    input_features=['input_1', 'input_2', 'input_3'],
    output_features=['output_1', 'output_2', 'output_3'],
    batch_size=32,
    scale=True                     # 是否标准化
)

# 获取数据加载器
train_loader = data_loader.get_train_loader()
val_loader = data_loader.get_val_loader()
test_loader = data_loader.get_test_loader()

# 创建SCINet模型
model = SCINet(
    output_len=24,    # 预测长度
    input_len=96,     # 输入长度
    input_dim=3,      # 输入特征数
    hid_size=1.0,
    num_stacks=1,
    num_levels=3
)

# 训练循环
for batch_x, batch_y in train_loader:
    # batch_x: [batch_size, seq_len, 3]
    # batch_y: [batch_size, pred_len, 3]
    outputs = model(batch_x)
    # 计算损失和反向传播...
```

### 3. 运行示例

```bash
# 运行快速测试
python example_usage.py

# 运行完整训练
python run_custom.py --data_path your_data.csv --epochs 100
```

## 详细参数说明

### CustomDataLoader参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `data_path` | str | - | 数据文件路径 |
| `seq_len` | int | 96 | 输入序列长度（回看窗口） |
| `pred_len` | int | 24 | 预测序列长度（预测窗口） |
| `input_features` | list | ['input_1', 'input_2', 'input_3'] | 输入特征列名 |
| `output_features` | list | ['output_1', 'output_2', 'output_3'] | 输出特征列名 |
| `batch_size` | int | 32 | 批次大小 |
| `scale` | bool | True | 是否进行标准化 |
| `train_ratio` | float | 0.7 | 训练集比例 |
| `val_ratio` | float | 0.2 | 验证集比例 |
| `num_workers` | int | 0 | 数据加载器工作进程数 |

### SCINet模型参数

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `input_dim` | 输入特征数 | 3（固定） |
| `seq_len` | 输入序列长度 | 96, 192, 384 |
| `pred_len` | 预测序列长度 | 24, 48, 96 |
| `num_levels` | SCINet层数 | 3, 4, 5 |
| `hidden_size` | 隐藏层大小 | 0.5, 1.0, 2.0 |

**重要约束**: `seq_len` 必须能被 `2^num_levels` 整除

## 数据流程图

```
原始数据 → 特征提取 → 数据分割 → 标准化 → 序列切片 → 批次加载
   ↓           ↓          ↓        ↓        ↓         ↓
CSV文件 → 6列数据 → 训练/验证/测试 → Z-score → 滑动窗口 → DataLoader
```

## 使用场景

这个DataLoader适用于以下时间序列预测任务：

1. **多变量输入-多变量输出预测**
   - 例如：根据温度、湿度、压力预测未来的风速、降雨量、能见度

2. **传感器数据预测**
   - 例如：根据多个传感器读数预测设备状态参数

3. **金融时间序列**
   - 例如：根据多个经济指标预测股价、汇率、商品价格

4. **工业过程控制**
   - 例如：根据工艺参数预测产品质量指标

## 完整训练示例

```bash
# 使用自定义数据训练SCINet
python run_custom.py \
    --data_path ./data/my_dataset.csv \
    --input_features temp humidity pressure \
    --output_features wind_speed rainfall visibility \
    --seq_len 96 \
    --pred_len 24 \
    --batch_size 32 \
    --epochs 100 \
    --lr 0.001 \
    --hidden_size 1.0 \
    --levels 3 \
    --stacks 1
```

## 注意事项

1. **数据质量**: 确保数据没有缺失值或异常值
2. **序列长度**: `seq_len` 必须满足 `seq_len % (2^levels) == 0`
3. **内存使用**: 大批次大小可能导致内存不足
4. **标准化**: 建议开启标准化以提高训练稳定性
5. **特征工程**: 可以在数据预处理阶段添加更多特征

## 故障排除

### 常见错误

1. **维度不匹配**
   ```
   错误: 输入特征必须是3个
   解决: 确保input_features列表长度为3
   ```

2. **序列长度约束**
   ```
   错误: seq_len必须能被2^levels整除
   解决: 调整seq_len或levels参数
   ```

3. **数据列缺失**
   ```
   错误: 数据中缺少以下列: ['xxx']
   解决: 检查CSV文件列名是否正确
   ```

### 性能优化

1. **增加num_workers**: 在多核CPU上可以加速数据加载
2. **调整batch_size**: 根据GPU内存调整批次大小
3. **使用混合精度**: 添加`--use_amp`参数

## 扩展功能

如果需要支持更多输入/输出特征，可以修改以下部分：

```python
# 在CustomDataset.__init__中修改
assert len(input_features) == N, f"输入特征必须是{N}个"
assert len(output_features) == M, f"输出特征必须是{M}个"

# 在SCINet模型中修改
model = SCINet(input_dim=N, ...)  # N为输入特征数
```

## 联系支持

如果遇到问题，请检查：
1. 数据格式是否正确
2. 参数设置是否合理
3. 依赖包是否安装完整

更多详细信息请参考原始SCINet论文和代码库。
