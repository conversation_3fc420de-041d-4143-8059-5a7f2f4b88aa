import argparse
import os
import torch
import torch.nn as nn
import numpy as np
from torch.utils.tensorboard import SummaryWriter
from torch import optim
import warnings
warnings.filterwarnings('ignore')

# 导入自定义数据加载器和SCINet模型
from data_process.custom_dataloader import CustomDataLoader
from models.SCINet import SCINet
from utils.tools import EarlyStopping, adjust_learning_rate


def train_model(args):
    """训练模型"""
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and args.use_gpu else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建数据加载器
    data_loader = CustomDataLoader(
        data_path=args.data_path,
        seq_len=args.seq_len,
        pred_len=args.pred_len,
        input_features=args.input_features,
        output_features=args.output_features,
        batch_size=args.batch_size,
        scale=args.scale,
        train_ratio=args.train_ratio,
        val_ratio=args.val_ratio,
        num_workers=args.num_workers
    )
    
    # 获取数据加载器
    train_loader = data_loader.get_train_loader()
    val_loader = data_loader.get_val_loader()
    test_loader = data_loader.get_test_loader()
    
    print(f"训练集样本数: {len(train_loader.dataset)}")
    print(f"验证集样本数: {len(val_loader.dataset)}")
    print(f"测试集样本数: {len(test_loader.dataset)}")
    
    # 创建模型
    model = SCINet(
        output_len=args.pred_len,
        input_len=args.seq_len,
        input_dim=3,  # 3个输入特征
        hid_size=args.hidden_size,
        num_stacks=args.stacks,
        num_levels=args.levels,
        num_decoder_layer=args.num_decoder_layer,
        concat_len=args.concat_len,
        groups=args.groups,
        kernel=args.kernel,
        dropout=args.dropout,
        single_step_output_One=args.single_step_output_One,
        positionalE=args.positionalEcoding,
        modified=True,
        RIN=args.RIN
    ).to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")
    
    # 损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=args.lr)
    
    # 早停机制
    early_stopping = EarlyStopping(patience=args.patience, verbose=True)
    
    # 训练循环
    model.train()
    train_losses = []
    val_losses = []
    
    for epoch in range(args.epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_steps = 0
        
        for batch_x, batch_y in train_loader:
            batch_x = batch_x.to(device)  # [batch_size, seq_len, 3]
            batch_y = batch_y.to(device)  # [batch_size, pred_len, 3]
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(batch_x)  # [batch_size, pred_len, 3]
            
            # 计算损失
            loss = criterion(outputs, batch_y)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_steps += 1
        
        avg_train_loss = train_loss / train_steps
        train_losses.append(avg_train_loss)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_steps = 0
        
        with torch.no_grad():
            for batch_x, batch_y in val_loader:
                batch_x = batch_x.to(device)
                batch_y = batch_y.to(device)
                
                outputs = model(batch_x)
                loss = criterion(outputs, batch_y)
                
                val_loss += loss.item()
                val_steps += 1
        
        avg_val_loss = val_loss / val_steps
        val_losses.append(avg_val_loss)
        
        print(f'Epoch [{epoch+1}/{args.epochs}], Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}')
        
        # 早停检查
        early_stopping(avg_val_loss, model)
        if early_stopping.early_stop:
            print("Early stopping")
            break
        
        # 学习率调整
        adjust_learning_rate(optimizer, epoch + 1, args)
    
    # 测试阶段
    model.eval()
    test_loss = 0.0
    test_steps = 0
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for batch_x, batch_y in test_loader:
            batch_x = batch_x.to(device)
            batch_y = batch_y.to(device)
            
            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)
            
            test_loss += loss.item()
            test_steps += 1
            
            # 收集预测结果
            all_predictions.append(outputs.cpu().numpy())
            all_targets.append(batch_y.cpu().numpy())
    
    avg_test_loss = test_loss / test_steps
    print(f'Test Loss: {avg_test_loss:.6f}')
    
    # 合并所有预测结果
    all_predictions = np.concatenate(all_predictions, axis=0)
    all_targets = np.concatenate(all_targets, axis=0)
    
    # 反标准化（如果需要）
    if args.scale:
        input_scaler, output_scaler = data_loader.get_scalers()
        all_predictions_orig = output_scaler.inverse_transform(
            all_predictions.reshape(-1, 3)).reshape(all_predictions.shape)
        all_targets_orig = output_scaler.inverse_transform(
            all_targets.reshape(-1, 3)).reshape(all_targets.shape)
    else:
        all_predictions_orig = all_predictions
        all_targets_orig = all_targets
    
    # 计算评估指标
    mse = np.mean((all_predictions_orig - all_targets_orig) ** 2)
    mae = np.mean(np.abs(all_predictions_orig - all_targets_orig))
    rmse = np.sqrt(mse)
    
    print(f"测试集评估指标:")
    print(f"MSE: {mse:.6f}")
    print(f"MAE: {mae:.6f}")
    print(f"RMSE: {rmse:.6f}")
    
    # 保存模型
    if args.save_model:
        torch.save(model.state_dict(), f'scinet_custom_{args.model_name}.pth')
        print(f"模型已保存为: scinet_custom_{args.model_name}.pth")
    
    return model, train_losses, val_losses


def main():
    parser = argparse.ArgumentParser(description='SCINet on Custom Dataset')
    
    # 数据设置
    parser.add_argument('--data_path', type=str, default='sample_data.csv', help='数据文件路径')
    parser.add_argument('--input_features', type=str, nargs=3, 
                       default=['input_1', 'input_2', 'input_3'], help='输入特征列名')
    parser.add_argument('--output_features', type=str, nargs=3,
                       default=['output_1', 'output_2', 'output_3'], help='输出特征列名')
    parser.add_argument('--seq_len', type=int, default=96, help='输入序列长度')
    parser.add_argument('--pred_len', type=int, default=24, help='预测序列长度')
    parser.add_argument('--scale', type=bool, default=True, help='是否标准化')
    parser.add_argument('--train_ratio', type=float, default=0.7, help='训练集比例')
    parser.add_argument('--val_ratio', type=float, default=0.2, help='验证集比例')
    
    # 模型设置
    parser.add_argument('--hidden_size', type=float, default=1.0, help='隐藏层大小')
    parser.add_argument('--stacks', type=int, default=1, help='SCINet块数量')
    parser.add_argument('--levels', type=int, default=3, help='SCINet层数')
    parser.add_argument('--num_decoder_layer', type=int, default=1, help='解码器层数')
    parser.add_argument('--concat_len', type=int, default=0, help='连接长度')
    parser.add_argument('--groups', type=int, default=1, help='组数')
    parser.add_argument('--kernel', type=int, default=5, help='卷积核大小')
    parser.add_argument('--dropout', type=float, default=0.5, help='Dropout率')
    parser.add_argument('--single_step_output_One', type=int, default=0, help='单步输出')
    parser.add_argument('--positionalEcoding', type=bool, default=False, help='位置编码')
    parser.add_argument('--RIN', type=bool, default=False, help='可逆实例归一化')
    
    # 训练设置
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--lr', type=float, default=0.001, help='学习率')
    parser.add_argument('--patience', type=int, default=10, help='早停耐心值')
    parser.add_argument('--lradj', type=int, default=1, help='学习率调整')
    
    # 其他设置
    parser.add_argument('--use_gpu', type=bool, default=True, help='使用GPU')
    parser.add_argument('--num_workers', type=int, default=0, help='数据加载器工作进程数')
    parser.add_argument('--save_model', type=bool, default=True, help='保存模型')
    parser.add_argument('--model_name', type=str, default='test', help='模型名称')
    
    args = parser.parse_args()
    
    # 检查seq_len是否满足SCINet的约束条件
    if args.seq_len % (2 ** args.levels) != 0:
        raise ValueError(f"seq_len ({args.seq_len}) 必须能被 2^levels (2^{args.levels}={2**args.levels}) 整除")
    
    print("开始训练...")
    print(f"参数设置: {args}")
    
    # 训练模型
    model, train_losses, val_losses = train_model(args)
    
    print("训练完成!")


if __name__ == "__main__":
    main()
