import torch
import torch.utils.data as torch_data
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from torch.utils.data import Dataset, DataLoader
import warnings
warnings.filterwarnings('ignore')


class CustomDataset(Dataset):
    """
    自定义数据集类，适用于三个输入参数、三个输出参数的时间序列预测任务
    
    Args:
        data_path: 数据文件路径
        seq_len: 输入序列长度（回看窗口）
        pred_len: 预测序列长度（预测窗口）
        input_features: 输入特征列名列表，长度为3
        output_features: 输出特征列名列表，长度为3
        flag: 数据集类型 ('train', 'val', 'test')
        scale: 是否进行标准化
        train_ratio: 训练集比例
        val_ratio: 验证集比例
    """
    
    def __init__(self, data_path, seq_len=96, pred_len=24, 
                 input_features=None, output_features=None,
                 flag='train', scale=True, train_ratio=0.7, val_ratio=0.2):
        
        # 参数设置
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.flag = flag
        self.scale = scale
        
        # 特征设置
        if input_features is None:
            self.input_features = ['input_1', 'input_2', 'input_3']
        else:
            assert len(input_features) == 3, "输入特征必须是3个"
            self.input_features = input_features
            
        if output_features is None:
            self.output_features = ['output_1', 'output_2', 'output_3']
        else:
            assert len(output_features) == 3, "输出特征必须是3个"
            self.output_features = output_features
        
        # 数据集分割比例
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = 1 - train_ratio - val_ratio
        
        # 标准化器
        self.input_scaler = StandardScaler()
        self.output_scaler = StandardScaler()
        
        # 读取和处理数据
        self.__read_data__(data_path)
    
    def __read_data__(self, data_path):
        """读取和预处理数据"""
        # 读取数据
        if data_path.endswith('.csv'):
            df_raw = pd.read_csv(data_path)
        elif data_path.endswith('.txt'):
            df_raw = pd.read_csv(data_path, delimiter='\t')  # 假设是制表符分隔
        else:
            raise ValueError("不支持的文件格式，请使用CSV或TXT文件")
        
        # 检查必要的列是否存在
        required_cols = self.input_features + self.output_features
        missing_cols = [col for col in required_cols if col not in df_raw.columns]
        if missing_cols:
            raise ValueError(f"数据中缺少以下列: {missing_cols}")
        
        # 提取输入和输出特征
        input_data = df_raw[self.input_features].values
        output_data = df_raw[self.output_features].values
        
        # 数据长度
        data_len = len(df_raw)
        
        # 计算分割点
        train_end = int(data_len * self.train_ratio)
        val_end = int(data_len * (self.train_ratio + self.val_ratio))
        
        # 根据flag选择数据范围
        if self.flag == 'train':
            start_idx = 0
            end_idx = train_end
        elif self.flag == 'val':
            start_idx = train_end - self.seq_len  # 保证验证集有足够的历史数据
            end_idx = val_end
        elif self.flag == 'test':
            start_idx = val_end - self.seq_len  # 保证测试集有足够的历史数据
            end_idx = data_len
        else:
            raise ValueError("flag必须是'train', 'val', 或'test'")
        
        # 提取对应范围的数据
        self.input_data = input_data[start_idx:end_idx]
        self.output_data = output_data[start_idx:end_idx]
        
        # 标准化处理
        if self.scale:
            if self.flag == 'train':
                # 训练集：拟合标准化器
                self.input_scaler.fit(self.input_data)
                self.output_scaler.fit(self.output_data)
                self.input_data = self.input_scaler.transform(self.input_data)
                self.output_data = self.output_scaler.transform(self.output_data)
            else:
                # 验证集和测试集：使用训练集的标准化器
                # 注意：这里需要传入训练集的标准化器，实际使用时需要保存和加载
                print("警告：验证集和测试集需要使用训练集的标准化器")
                self.input_data = self.input_scaler.transform(self.input_data)
                self.output_data = self.output_scaler.transform(self.output_data)
        
        print(f"{self.flag}集数据形状 - 输入: {self.input_data.shape}, 输出: {self.output_data.shape}")
    
    def __getitem__(self, index):
        """获取单个样本"""
        # 输入序列的起始和结束位置
        s_begin = index
        s_end = s_begin + self.seq_len
        
        # 输出序列的起始和结束位置
        r_begin = s_end
        r_end = r_begin + self.pred_len
        
        # 提取输入序列 (seq_len, 3)
        seq_x = self.input_data[s_begin:s_end]
        
        # 提取输出序列 (pred_len, 3)
        seq_y = self.output_data[r_begin:r_end]
        
        # 转换为tensor
        seq_x = torch.from_numpy(seq_x).float()
        seq_y = torch.from_numpy(seq_y).float()
        
        return seq_x, seq_y
    
    def __len__(self):
        """数据集长度"""
        return len(self.input_data) - self.seq_len - self.pred_len + 1
    
    def inverse_transform_input(self, data):
        """输入数据反标准化"""
        if self.scale:
            return self.input_scaler.inverse_transform(data)
        return data
    
    def inverse_transform_output(self, data):
        """输出数据反标准化"""
        if self.scale:
            return self.output_scaler.inverse_transform(data)
        return data


class CustomDataLoader:
    """
    自定义数据加载器管理类
    """
    
    def __init__(self, data_path, seq_len=96, pred_len=24,
                 input_features=None, output_features=None,
                 batch_size=32, scale=True, 
                 train_ratio=0.7, val_ratio=0.2,
                 num_workers=0):
        
        self.data_path = data_path
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.input_features = input_features
        self.output_features = output_features
        self.batch_size = batch_size
        self.scale = scale
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.num_workers = num_workers
        
        # 创建数据集
        self.train_dataset = CustomDataset(
            data_path=data_path,
            seq_len=seq_len,
            pred_len=pred_len,
            input_features=input_features,
            output_features=output_features,
            flag='train',
            scale=scale,
            train_ratio=train_ratio,
            val_ratio=val_ratio
        )
        
        self.val_dataset = CustomDataset(
            data_path=data_path,
            seq_len=seq_len,
            pred_len=pred_len,
            input_features=input_features,
            output_features=output_features,
            flag='val',
            scale=scale,
            train_ratio=train_ratio,
            val_ratio=val_ratio
        )
        
        self.test_dataset = CustomDataset(
            data_path=data_path,
            seq_len=seq_len,
            pred_len=pred_len,
            input_features=input_features,
            output_features=output_features,
            flag='test',
            scale=scale,
            train_ratio=train_ratio,
            val_ratio=val_ratio
        )
        
        # 同步标准化器（让验证集和测试集使用训练集的标准化器）
        if scale:
            self.val_dataset.input_scaler = self.train_dataset.input_scaler
            self.val_dataset.output_scaler = self.train_dataset.output_scaler
            self.test_dataset.input_scaler = self.train_dataset.input_scaler
            self.test_dataset.output_scaler = self.train_dataset.output_scaler
            
            # 重新标准化验证集和测试集
            self.val_dataset.input_data = self.val_dataset.input_scaler.transform(
                self.val_dataset.input_data)
            self.val_dataset.output_data = self.val_dataset.output_scaler.transform(
                self.val_dataset.output_data)
            self.test_dataset.input_data = self.test_dataset.input_scaler.transform(
                self.test_dataset.input_data)
            self.test_dataset.output_data = self.test_dataset.output_scaler.transform(
                self.test_dataset.output_data)
    
    def get_train_loader(self):
        """获取训练数据加载器"""
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            drop_last=True
        )
    
    def get_val_loader(self):
        """获取验证数据加载器"""
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            drop_last=False
        )
    
    def get_test_loader(self):
        """获取测试数据加载器"""
        return DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            drop_last=False
        )
    
    def get_scalers(self):
        """获取标准化器"""
        return self.train_dataset.input_scaler, self.train_dataset.output_scaler


# 使用示例和测试函数
def create_sample_data(file_path, num_samples=1000):
    """创建示例数据用于测试"""
    np.random.seed(42)

    # 生成时间序列数据
    time_steps = np.arange(num_samples)

    # 输入特征（3个）
    input_1 = np.sin(time_steps * 0.1) + np.random.normal(0, 0.1, num_samples)
    input_2 = np.cos(time_steps * 0.05) + np.random.normal(0, 0.1, num_samples)
    input_3 = np.sin(time_steps * 0.02) * np.cos(time_steps * 0.03) + np.random.normal(0, 0.1, num_samples)

    # 输出特征（3个），与输入有一定的相关性
    output_1 = input_1 * 0.8 + input_2 * 0.2 + np.random.normal(0, 0.05, num_samples)
    output_2 = input_2 * 0.7 + input_3 * 0.3 + np.random.normal(0, 0.05, num_samples)
    output_3 = input_3 * 0.6 + input_1 * 0.4 + np.random.normal(0, 0.05, num_samples)

    # 创建DataFrame
    df = pd.DataFrame({
        'input_1': input_1,
        'input_2': input_2,
        'input_3': input_3,
        'output_1': output_1,
        'output_2': output_2,
        'output_3': output_3
    })

    # 保存到文件
    df.to_csv(file_path, index=False)
    print(f"示例数据已保存到: {file_path}")
    return df


if __name__ == "__main__":
    # 创建示例数据
    sample_data_path = "sample_data.csv"
    create_sample_data(sample_data_path, num_samples=1000)

    # 测试数据加载器
    data_loader = CustomDataLoader(
        data_path=sample_data_path,
        seq_len=96,
        pred_len=24,
        input_features=['input_1', 'input_2', 'input_3'],
        output_features=['output_1', 'output_2', 'output_3'],
        batch_size=32,
        scale=True
    )

    # 获取数据加载器
    train_loader = data_loader.get_train_loader()
    val_loader = data_loader.get_val_loader()
    test_loader = data_loader.get_test_loader()

    # 测试数据形状
    for batch_x, batch_y in train_loader:
        print(f"训练批次 - 输入形状: {batch_x.shape}, 输出形状: {batch_y.shape}")
        print(f"输入维度: [batch_size, seq_len, input_features] = {batch_x.shape}")
        print(f"输出维度: [batch_size, pred_len, output_features] = {batch_y.shape}")
        break

    print(f"训练集样本数: {len(train_loader.dataset)}")
    print(f"验证集样本数: {len(val_loader.dataset)}")
    print(f"测试集样本数: {len(test_loader.dataset)}")
