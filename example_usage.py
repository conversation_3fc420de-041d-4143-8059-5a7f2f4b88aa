"""
SCINet自定义数据集使用示例

这个脚本展示了如何使用自定义DataLoader与SCINet模型进行训练
适用于三个输入参数、三个输出参数的时间序列预测任务
"""

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from data_process.custom_dataloader import CustomDataLoader, create_sample_data
from models.SCINet import SCINet


def quick_test():
    """快速测试示例"""
    print("=== SCINet自定义数据集快速测试 ===")
    
    # 1. 创建示例数据
    print("1. 创建示例数据...")
    data_path = "example_data.csv"
    df = create_sample_data(data_path, num_samples=1000)
    print(f"数据形状: {df.shape}")
    print(f"数据列: {df.columns.tolist()}")
    print(f"数据预览:\n{df.head()}")
    
    # 2. 创建数据加载器
    print("\n2. 创建数据加载器...")
    data_loader = CustomDataLoader(
        data_path=data_path,
        seq_len=96,  # 输入序列长度
        pred_len=24,  # 预测序列长度
        input_features=['input_1', 'input_2', 'input_3'],
        output_features=['output_1', 'output_2', 'output_3'],
        batch_size=16,
        scale=True,
        train_ratio=0.7,
        val_ratio=0.2
    )
    
    # 获取数据加载器
    train_loader = data_loader.get_train_loader()
    val_loader = data_loader.get_val_loader()
    test_loader = data_loader.get_test_loader()
    
    print(f"训练集批次数: {len(train_loader)}")
    print(f"验证集批次数: {len(val_loader)}")
    print(f"测试集批次数: {len(test_loader)}")
    
    # 3. 检查数据形状
    print("\n3. 检查数据形状...")
    for batch_x, batch_y in train_loader:
        print(f"输入批次形状: {batch_x.shape}")  # [batch_size, seq_len, input_features]
        print(f"输出批次形状: {batch_y.shape}")  # [batch_size, pred_len, output_features]
        print(f"输入数据类型: {batch_x.dtype}")
        print(f"输出数据类型: {batch_y.dtype}")
        break
    
    # 4. 创建SCINet模型
    print("\n4. 创建SCINet模型...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    model = SCINet(
        output_len=24,    # 预测长度
        input_len=96,     # 输入长度
        input_dim=3,      # 输入特征数（3个输入参数）
        hid_size=1.0,     # 隐藏层大小
        num_stacks=1,     # SCINet块数量
        num_levels=3,     # SCINet层数
        num_decoder_layer=1,
        concat_len=0,
        groups=1,
        kernel=5,
        dropout=0.5,
        single_step_output_One=0,
        positionalE=False,
        modified=True,
        RIN=False
    ).to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")
    
    # 5. 测试前向传播
    print("\n5. 测试前向传播...")
    model.eval()
    with torch.no_grad():
        for batch_x, batch_y in train_loader:
            batch_x = batch_x.to(device)
            batch_y = batch_y.to(device)
            
            # 前向传播
            outputs = model(batch_x)
            
            print(f"模型输入形状: {batch_x.shape}")
            print(f"模型输出形状: {outputs.shape}")
            print(f"真实标签形状: {batch_y.shape}")
            
            # 计算损失
            loss = torch.nn.MSELoss()(outputs, batch_y)
            print(f"MSE损失: {loss.item():.6f}")
            break
    
    print("\n=== 快速测试完成 ===")
    return data_loader, model


def simple_training_example():
    """简单训练示例"""
    print("\n=== 简单训练示例 ===")
    
    # 创建数据和模型
    data_loader, model = quick_test()
    
    # 设置训练参数
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    criterion = torch.nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    train_loader = data_loader.get_train_loader()
    val_loader = data_loader.get_val_loader()
    
    # 简单训练循环（只训练几个epoch）
    num_epochs = 5
    print(f"\n开始训练 {num_epochs} 个epoch...")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_steps = 0
        
        for batch_x, batch_y in train_loader:
            batch_x = batch_x.to(device)
            batch_y = batch_y.to(device)
            
            optimizer.zero_grad()
            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_steps += 1
        
        avg_train_loss = train_loss / train_steps
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_steps = 0
        
        with torch.no_grad():
            for batch_x, batch_y in val_loader:
                batch_x = batch_x.to(device)
                batch_y = batch_y.to(device)
                
                outputs = model(batch_x)
                loss = criterion(outputs, batch_y)
                
                val_loss += loss.item()
                val_steps += 1
        
        avg_val_loss = val_loss / val_steps
        
        print(f'Epoch [{epoch+1}/{num_epochs}], Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}')
    
    print("简单训练完成!")
    return model


def visualize_predictions(model, data_loader, num_samples=3):
    """可视化预测结果"""
    print("\n=== 可视化预测结果 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    test_loader = data_loader.get_test_loader()
    
    model.eval()
    with torch.no_grad():
        for i, (batch_x, batch_y) in enumerate(test_loader):
            if i >= num_samples:
                break
                
            batch_x = batch_x.to(device)
            batch_y = batch_y.to(device)
            
            outputs = model(batch_x)
            
            # 取第一个样本进行可视化
            input_seq = batch_x[0].cpu().numpy()  # [seq_len, 3]
            true_output = batch_y[0].cpu().numpy()  # [pred_len, 3]
            pred_output = outputs[0].cpu().numpy()  # [pred_len, 3]
            
            # 创建图形
            fig, axes = plt.subplots(3, 1, figsize=(12, 10))
            
            for feature_idx in range(3):
                ax = axes[feature_idx]
                
                # 绘制输入序列
                input_x = range(len(input_seq))
                ax.plot(input_x, input_seq[:, feature_idx], 'b-', label='Input', alpha=0.7)
                
                # 绘制真实输出
                output_x = range(len(input_seq), len(input_seq) + len(true_output))
                ax.plot(output_x, true_output[:, feature_idx], 'g-', label='True', linewidth=2)
                
                # 绘制预测输出
                ax.plot(output_x, pred_output[:, feature_idx], 'r--', label='Predicted', linewidth=2)
                
                ax.set_title(f'Feature {feature_idx + 1} - Sample {i + 1}')
                ax.legend()
                ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(f'prediction_sample_{i+1}.png', dpi=150, bbox_inches='tight')
            plt.show()
            
            # 计算误差
            mse = np.mean((pred_output - true_output) ** 2)
            mae = np.mean(np.abs(pred_output - true_output))
            print(f"样本 {i+1} - MSE: {mse:.6f}, MAE: {mae:.6f}")


def main():
    """主函数"""
    print("SCINet自定义数据集使用示例")
    print("=" * 50)
    
    # 1. 快速测试
    data_loader, model = quick_test()
    
    # 2. 简单训练
    trained_model = simple_training_example()
    
    # 3. 可视化预测结果
    try:
        visualize_predictions(trained_model, data_loader, num_samples=2)
    except ImportError:
        print("matplotlib未安装，跳过可视化")
    
    print("\n" + "=" * 50)
    print("示例运行完成!")
    print("\n使用说明:")
    print("1. 准备你的数据文件（CSV格式）")
    print("2. 确保数据包含3个输入特征列和3个输出特征列")
    print("3. 修改 input_features 和 output_features 参数")
    print("4. 调整 seq_len 和 pred_len 参数")
    print("5. 运行完整训练: python run_custom.py --data_path your_data.csv")


if __name__ == "__main__":
    main()
